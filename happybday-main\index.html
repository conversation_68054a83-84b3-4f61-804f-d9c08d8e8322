<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>💖 Happy Birthday My Love 💖</title>
    <link
      href="https://fonts.googleapis.com/css2?family=Dancing+Script:wght@400;700&family=Poppins:wght@300;400;600&family=Great+Vibes&display=swap"
      rel="stylesheet"
    />
    <link type="text/css" rel="stylesheet" href="./file/default.css" />
    <link type="text/css" rel="stylesheet" href="./file/enhanced.css" />
    <script type="text/javascript" src="./file/jquery.min.js"></script>
    <script type="text/javascript" src="./file/jscex.min.js"></script>
    <script type="text/javascript" src="./file/jscex-parser.js"></script>
    <script type="text/javascript" src="./file/jscex-jit.js"></script>
    <script
      type="text/javascript"
      src="./file/jscex-builderbase.min.js"
    ></script>
    <script type="text/javascript" src="./file/jscex-async.min.js"></script>
    <script
      type="text/javascript"
      src="./file/jscex-async-powerpack.min.js"
    ></script>
    <script
      type="text/javascript"
      src="./file/functions.js"
      charset="utf-8"
    ></script>
    <script
      type="text/javascript"
      src="./file/love.js"
      charset="utf-8"
    ></script>
    <style>
      /* Enhanced Styles */
      .floating-hearts {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        pointer-events: none;
        z-index: 1000;
      }

      .heart {
        position: absolute;
        color: #ff69b4;
        font-size: 20px;
        animation: float 6s ease-in-out infinite;
        opacity: 0.7;
      }

      @keyframes float {
        0% {
          transform: translateY(100vh) rotate(0deg);
          opacity: 0;
        }
        10% {
          opacity: 0.7;
        }
        90% {
          opacity: 0.7;
        }
        100% {
          transform: translateY(-100px) rotate(360deg);
          opacity: 0;
        }
      }

      .sparkle {
        position: absolute;
        width: 4px;
        height: 4px;
        background: #fff;
        border-radius: 50%;
        animation: sparkle 2s ease-in-out infinite;
      }

      @keyframes sparkle {
        0%,
        100% {
          opacity: 0;
          transform: scale(0);
        }
        50% {
          opacity: 1;
          transform: scale(1);
        }
      }

      .title-container {
        position: fixed;
        top: 20px;
        left: 50%;
        transform: translateX(-50%);
        z-index: 100;
        text-align: center;
      }

      .main-title {
        font-family: "Great Vibes", cursive;
        font-size: 3.5rem;
        color: #ff69b4;
        text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5),
          0 0 20px rgba(255, 105, 180, 0.5);
        margin: 0;
        animation: glow 2s ease-in-out infinite alternate;
      }

      @keyframes glow {
        from {
          text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5),
            0 0 20px rgba(255, 105, 180, 0.5);
        }
        to {
          text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5),
            0 0 30px rgba(255, 105, 180, 0.8), 0 0 40px rgba(255, 105, 180, 0.6);
        }
      }

      .music-controls {
        position: fixed;
        top: 20px;
        right: 20px;
        z-index: 100;
        background: rgba(255, 255, 255, 0.1);
        backdrop-filter: blur(10px);
        border-radius: 15px;
        padding: 10px;
        border: 1px solid rgba(255, 255, 255, 0.2);
      }

      .music-btn {
        background: linear-gradient(45deg, #ff69b4, #ff1493);
        border: none;
        color: white;
        padding: 10px 15px;
        border-radius: 50%;
        cursor: pointer;
        font-size: 16px;
        transition: all 0.3s ease;
        box-shadow: 0 4px 15px rgba(255, 105, 180, 0.3);
      }

      .music-btn:hover {
        transform: scale(1.1);
        box-shadow: 0 6px 20px rgba(255, 105, 180, 0.5);
      }

      .click-instruction {
        position: fixed;
        bottom: 30px;
        left: 50%;
        transform: translateX(-50%);
        z-index: 100;
        background: rgba(255, 255, 255, 0.1);
        backdrop-filter: blur(10px);
        padding: 15px 30px;
        border-radius: 25px;
        border: 1px solid rgba(255, 255, 255, 0.2);
        font-family: "Poppins", sans-serif;
        color: #fff;
        text-align: center;
        animation: pulse 2s ease-in-out infinite;
      }

      @keyframes pulse {
        0%,
        100% {
          transform: translateX(-50%) scale(1);
        }
        50% {
          transform: translateX(-50%) scale(1.05);
        }
      }
    </style>
    <script>
      let audioPlaying = false;

      function toggleAudio() {
        var audio = document.getElementById("myAudio");
        var btn = document.getElementById("musicBtn");

        if (audioPlaying) {
          audio.pause();
          btn.innerHTML = "🔇";
          audioPlaying = false;
        } else {
          audio.play();
          btn.innerHTML = "🎵";
          audioPlaying = true;
        }
      }

      function playAudio() {
        var audio = document.getElementById("myAudio");
        var btn = document.getElementById("musicBtn");
        audio.play();
        btn.innerHTML = "🎵";
        audioPlaying = true;
      }

      // Create floating hearts
      function createFloatingHearts() {
        const heartsContainer = document.querySelector(".floating-hearts");
        const hearts = [
          "💖",
          "💕",
          "💗",
          "💓",
          "💝",
          "💘",
          "💞",
          "💟",
          "❤️",
          "🧡",
          "💛",
          "💚",
          "💙",
          "💜",
          "🤍",
          "🖤",
          "🤎",
        ];

        setInterval(() => {
          const heart = document.createElement("div");
          heart.className = "heart";
          heart.innerHTML = hearts[Math.floor(Math.random() * hearts.length)];
          heart.style.left = Math.random() * 100 + "%";
          heart.style.animationDuration = Math.random() * 3 + 3 + "s";
          heart.style.fontSize = Math.random() * 10 + 15 + "px";
          heartsContainer.appendChild(heart);

          setTimeout(() => {
            heart.remove();
          }, 6000);
        }, 800);
      }

      // Create sparkles
      function createSparkles() {
        setInterval(() => {
          const sparkle = document.createElement("div");
          sparkle.className = "sparkle";
          sparkle.style.left = Math.random() * 100 + "%";
          sparkle.style.top = Math.random() * 100 + "%";
          sparkle.style.animationDelay = Math.random() * 2 + "s";
          document.body.appendChild(sparkle);

          setTimeout(() => {
            sparkle.remove();
          }, 2000);
        }, 300);
      }

      // Show love message
      function showLoveMessage() {
        const messages = [
          "I Love You! 💖",
          "You're My Everything! 💕",
          "Happy Birthday Beautiful! 🎉",
          "Forever Yours! 💗",
          "You Make Me Complete! 💞",
          "My Heart Belongs to You! ❤️",
        ];

        const message = messages[Math.floor(Math.random() * messages.length)];
        const loveDiv = document.createElement("div");
        loveDiv.className = "love-message";
        loveDiv.innerHTML = message;
        document.body.appendChild(loveDiv);

        setTimeout(() => {
          loveDiv.remove();
        }, 3000);
      }

      // Create confetti
      function createConfetti() {
        for (let i = 0; i < 50; i++) {
          setTimeout(() => {
            const confetti = document.createElement("div");
            confetti.className = "confetti";
            confetti.style.left = Math.random() * 100 + "%";
            confetti.style.animationDelay = Math.random() * 3 + "s";
            confetti.style.backgroundColor = [
              "#ff69b4",
              "#ff1493",
              "#ffc0cb",
              "#ffb6c1",
            ][Math.floor(Math.random() * 4)];
            document.body.appendChild(confetti);

            setTimeout(() => {
              confetti.remove();
            }, 3000);
          }, i * 100);
        }
      }

      // Enhanced particle system
      function createParticles() {
        setInterval(() => {
          const particle = document.createElement("div");
          particle.className = "particle";
          particle.style.left = Math.random() * 100 + "%";
          particle.style.top = Math.random() * 100 + "%";
          particle.style.animationDelay = Math.random() * 2 + "s";
          document.body.appendChild(particle);

          setTimeout(() => {
            particle.remove();
          }, 4000);
        }, 500);
      }

      window.addEventListener("load", () => {
        createFloatingHearts();
        createSparkles();
        createParticles();

        // Show confetti after 5 seconds
        setTimeout(createConfetti, 5000);

        // Show periodic love messages
        setInterval(() => {
          if (Math.random() < 0.3) {
            showLoveMessage();
          }
        }, 10000);
      });
    </script>
  </head>
  <body>
    <!-- Floating Hearts Container -->
    <div class="floating-hearts"></div>

    <!-- Title -->
    <div class="title-container">
      <h1 class="main-title">Happy Birthday My Love</h1>
    </div>

    <!-- Music Controls -->
    <div class="music-controls">
      <button
        class="music-btn"
        id="musicBtn"
        onclick="toggleAudio()"
        title="Toggle Music"
      >
        🔇
      </button>
    </div>

    <!-- Click Instruction -->
    <div class="click-instruction">
      💖 Click on the heart to grow our love tree 💖
    </div>

    <!-- Birthday Cake -->
    <div class="birthday-cake">🎂</div>

    <!-- Floating Balloons -->
    <div class="balloon">🎈</div>
    <div class="balloon">🎈</div>
    <div class="balloon">🎈</div>
    <div class="balloon">🎈</div>

    <!-- Interactive Hearts -->
    <div
      class="interactive-heart"
      style="top: 15%; left: 15%"
      onclick="showLoveMessage()"
    >
      💖
    </div>
    <div
      class="interactive-heart"
      style="top: 25%; right: 15%"
      onclick="showLoveMessage()"
    >
      💕
    </div>
    <div
      class="interactive-heart"
      style="bottom: 20%; left: 10%"
      onclick="showLoveMessage()"
    >
      💗
    </div>

    <div id="main">
      <div id="error">
        <a
          href="http://www.google.cn/chrome/intl/zh-CN/landing_chrome.html?hl=zh-CN&brand=CHMI"
          >Chrome</a
        >
        (<a href="http://firefox.com.cn/download/">Firefox</a>)
      </div>
      <audio autoplay="autoplay" height="100" width="100" id="myAudio" loop>
        <source src="aud.mp3" type="audio/mp3" />
        <embed height="100" width="100" src="aud.mp3" />
      </audio>
      <div id="wrap">
        <div id="text">
          <div id="code">
            <span class="say">My beautiful love 💞</span><br />
            <span class="say">Happy Birthday my darling 🎈</span><br />
            <span class="say">You are the light of my life ✨</span><br />
            <span class="say">May this year bring you endless joy 💕</span
            ><br />
            <span class="say">I love you more than words can say ❤️</span><br />
            <span class="say">And I will cherish you forever 🥺 ❤️</span><br />
            <span class="say">You make every day magical 🌟</span><br />
            <span class="say">Happy Birthday, my everything 💖😘</span><br />
          </div>
        </div>
        <div id="clock-box">
          <span id="clock">577 days 0 hours 0 minutes 0 seconds</span>
          <!-- Change to "6940 days..." if needed -->
        </div>
        <canvas id="canvas" width="1100" height="680"></canvas>
      </div>
    </div>

    <script>
      (function () {
        var canvas = $("#canvas");

        if (!canvas[0].getContext) {
          $("#error").show();
          return false;
        }

        var width = canvas.width();
        var height = canvas.height();
        canvas.attr("width", width);
        canvas.attr("height", height);

        var opts = {
          seed: {
            x: width / 2 - 20,
            color: "rgb(190, 26, 37)",
            scale: 2,
          },
          branch: [
            [
              535,
              680,
              570,
              250,
              500,
              200,
              30,
              100,
              [
                [
                  540,
                  500,
                  455,
                  417,
                  340,
                  400,
                  13,
                  100,
                  [[450, 435, 434, 430, 394, 395, 2, 40]],
                ],
                [
                  550,
                  445,
                  600,
                  356,
                  680,
                  345,
                  12,
                  100,
                  [[578, 400, 648, 409, 661, 426, 3, 80]],
                ],
                [539, 281, 537, 248, 534, 217, 3, 40],
                [
                  546,
                  397,
                  413,
                  247,
                  328,
                  244,
                  9,
                  80,
                  [
                    [427, 286, 383, 253, 371, 205, 2, 40],
                    [498, 345, 435, 315, 395, 330, 4, 60],
                  ],
                ],
                [
                  546,
                  357,
                  608,
                  252,
                  678,
                  221,
                  6,
                  100,
                  [[590, 293, 646, 277, 648, 271, 2, 80]],
                ],
              ],
            ],
          ],
          bloom: {
            num: 700,
            width: 1080,
            height: 650,
          },
          footer: {
            width: 1200,
            height: 5,
            speed: 10,
          },
        };

        var tree = new Tree(canvas[0], width, height, opts);
        var seed = tree.seed;
        var foot = tree.footer;
        var hold = 1;

        canvas
          .click(function (e) {
            playAudio();
            var offset = canvas.offset(),
              x,
              y;
            x = e.pageX - offset.left;
            y = e.pageY - offset.top;
            if (seed.hover(x, y)) {
              hold = 0;
              canvas.unbind("click");
              canvas.unbind("mousemove");
              canvas.removeClass("hand");
            }
          })
          .mousemove(function (e) {
            var offset = canvas.offset(),
              x,
              y;
            x = e.pageX - offset.left;
            y = e.pageY - offset.top;
            canvas.toggleClass("hand", seed.hover(x, y));
          });

        var seedAnimate = eval(
          Jscex.compile("async", function () {
            seed.draw();
            while (hold) {
              $await(Jscex.Async.sleep(10));
            }
            while (seed.canScale()) {
              seed.scale(0.95);
              $await(Jscex.Async.sleep(10));
            }
            while (seed.canMove()) {
              seed.move(0, 2);
              foot.draw();
              $await(Jscex.Async.sleep(10));
            }
          })
        );

        var growAnimate = eval(
          Jscex.compile("async", function () {
            do {
              tree.grow();
              $await(Jscex.Async.sleep(10));
            } while (tree.canGrow());
          })
        );

        var flowAnimate = eval(
          Jscex.compile("async", function () {
            do {
              tree.flower(2);
              $await(Jscex.Async.sleep(10));
            } while (tree.canFlower());
          })
        );

        var moveAnimate = eval(
          Jscex.compile("async", function () {
            tree.snapshot("p1", 240, 0, 610, 680);
            while (tree.move("p1", 500, 0)) {
              foot.draw();
              $await(Jscex.Async.sleep(10));
            }
            foot.draw();
            tree.snapshot("p2", 500, 0, 610, 680);

            canvas
              .parent()
              .css("background", "url(" + tree.toDataURL("image/png") + ")");
            canvas.css("background", "#ffe");
            $await(Jscex.Async.sleep(300));
            canvas.css("background", "none");
          })
        );

        var textAnimate = eval(
          Jscex.compile("async", function () {
            $("#code").show().typewriter();
          })
        );

        var runAsync = eval(
          Jscex.compile("async", function () {
            $await(seedAnimate());
            $await(growAnimate());
            $await(flowAnimate());
            $await(moveAnimate());
            textAnimate().start();
          })
        );

        runAsync().start();
      })();
    </script>
  </body>
</html>
