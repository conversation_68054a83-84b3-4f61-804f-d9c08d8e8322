<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xml:lang="en" xmlns="http://www.w3.org/1999/xhtml">
  <head>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
    <title>Happy Birthday Pihu! 🎉💖</title>
    <link type="text/css" rel="stylesheet" href="./file/default.css" />
    <script type="text/javascript" src="./file/jquery.min.js"></script>
    <script type="text/javascript" src="./file/jscex.min.js"></script>
    <script type="text/javascript" src="./file/jscex-parser.js"></script>
    <script type="text/javascript" src="./file/jscex-jit.js"></script>
    <script
      type="text/javascript"
      src="./file/jscex-builderbase.min.js"
    ></script>
    <script type="text/javascript" src="./file/jscex-async.min.js"></script>
    <script
      type="text/javascript"
      src="./file/jscex-async-powerpack.min.js"
    ></script>
    <script
      type="text/javascript"
      src="./file/functions.js"
      charset="utf-8"
    ></script>
    <script
      type="text/javascript"
      src="./file/love.js"
      charset="utf-8"
    ></script>
    <script>
      function playAudio() {
        var audio = document.getElementById("myAudio");
        audio.play();
      }
    </script>

    <style>
      /* Modern Design for Pihu's Birthday - Keeping Tree Animation */

      @import url("https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;600;700;800&display=swap");

      body {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        font-family: "Poppins", sans-serif;
        margin: 0;
        padding: 0;
        min-height: 100vh;
        position: relative;
        overflow-x: hidden;
      }

      /* Animated background particles */
      body::before {
        content: "";
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: radial-gradient(
            circle at 20% 50%,
            rgba(255, 255, 255, 0.1) 0%,
            transparent 50%
          ),
          radial-gradient(
            circle at 80% 20%,
            rgba(255, 107, 107, 0.1) 0%,
            transparent 50%
          ),
          radial-gradient(
            circle at 40% 80%,
            rgba(78, 205, 196, 0.1) 0%,
            transparent 50%
          );
        pointer-events: none;
        z-index: 1;
        animation: backgroundShift 10s ease-in-out infinite alternate;
      }

      @keyframes backgroundShift {
        0% {
          opacity: 0.3;
        }
        100% {
          opacity: 0.7;
        }
      }

      /* Message box styling */
      #message-box {
        position: fixed;
        bottom: 30px;
        left: 30px;
        color: rgba(255, 255, 255, 0.95);
        font-size: 14px;
        font-weight: 600;
        text-align: center;
        text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
        background: rgba(255, 255, 255, 0.15);
        backdrop-filter: blur(20px);
        padding: 12px 20px;
        border-radius: 25px;
        border: 1px solid rgba(255, 255, 255, 0.2);
        animation: messageGlow 3s ease-in-out infinite alternate;
        z-index: 100;
        max-width: 280px;
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
      }

      @keyframes messageGlow {
        0% {
          box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1),
            0 0 20px rgba(255, 255, 255, 0.2);
          transform: scale(1);
        }
        100% {
          box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15),
            0 0 30px rgba(255, 255, 255, 0.3);
          transform: scale(1.02);
        }
      }

      /* Enhanced clock styling */
      #clock-box {
        background: rgba(255, 255, 255, 0.15);
        backdrop-filter: blur(20px);
        border: 1px solid rgba(255, 255, 255, 0.2);
        border-radius: 20px;
        padding: 15px 25px;
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        color: rgba(255, 255, 255, 0.95);
        font-weight: 600;
        text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
      }

      /* Modern canvas styling */
      #canvas {
        border-radius: 20px;
        background: rgba(255, 255, 255, 0.05);
        backdrop-filter: blur(10px);
        box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
        border: 1px solid rgba(255, 255, 255, 0.1);
        transition: all 0.3s ease;
      }

      #canvas:hover {
        box-shadow: 0 20px 45px rgba(0, 0, 0, 0.15);
        transform: translateY(-2px);
      }

      /* Heart hover effect */
      .hand {
        cursor: pointer !important;
        filter: drop-shadow(0 0 15px rgba(255, 107, 107, 0.8));
      }

      /* Birthday greeting animation */
      @keyframes fadeInScale {
        0% {
          opacity: 0;
          transform: translate(-50%, -50%) scale(0.5);
        }
        100% {
          opacity: 1;
          transform: translate(-50%, -50%) scale(1);
        }
      }

      @keyframes fadeOut {
        0% {
          opacity: 1;
          transform: translate(-50%, -50%) scale(1);
        }
        100% {
          opacity: 0;
          transform: translate(-50%, -50%) scale(0.8);
        }
      }

      /* Modern pulse animation for click instruction */
      @keyframes modernPulse {
        0%,
        100% {
          box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1),
            0 0 20px rgba(255, 255, 255, 0.2);
          transform: translateX(-50%) scale(1);
        }
        50% {
          box-shadow: 0 15px 40px rgba(0, 0, 0, 0.15),
            0 0 30px rgba(255, 255, 255, 0.3);
          transform: translateX(-50%) scale(1.05);
        }
      }
    </style>
  </head>
  <body>
    <div id="main">
      <div id="error">
        <a
          href="http://www.google.cn/chrome/intl/zh-CN/landing_chrome.html?hl=zh-CN&brand=CHMI"
          >Chrome</a
        >
        (<a href="http://firefox.com.cn/download/">Firefox</a>)
      </div>
      <audio autoplay="autoplay" height="100" width="100" id="myAudio">
        <source src="aud.mp3" type="audio/mp3" />
        <embed height="100" width="100" src="aud.mp3" />
      </audio>
      <div id="wrap">
        <div id="text">
          <div id="code">
            <span class="say">Happy Birthday my dearest Pihu! 🎉�</span><br />
            <span class="say"
              >Yogita, you're the most amazing bestie ever! �</span
            ><br />
            <span class="say">On this special day - August 15th �</span><br />
            <span class="say">I wish you all the happiness in the world 💕</span
            ><br />
            <span class="say"
              >May your 20th year be filled with joy & success ✨</span
            ><br />
            <span class="say">You deserve all the love and blessings 🥰</span
            ><br />
            <span class="say"
              >Thank you for being such an incredible friend ❤️</span
            ><br />
            <span class="say"
              >Here's to many more years of friendship! 🥳🎂</span
            ><br />
          </div>
        </div>
        <div id="clock-box">
          <span id="clock">577 days 0 hours 0 minutes 0 seconds</span>
          <!-- Change to "6940 days..." if needed -->
        </div>
        <div id="message-box">PIHU HAS BEEN BLESSING THE WORLD SINCE</div>

        <!-- Click Instruction for Pihu -->
        <div
          id="click-instruction"
          style="
            position: absolute;
            bottom: 120px;
            left: 50%;
            transform: translateX(-50%);
            color: rgba(255, 255, 255, 0.95);
            font-size: 18px;
            font-weight: 600;
            text-align: center;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
            background: rgba(255, 255, 255, 0.15);
            backdrop-filter: blur(20px);
            padding: 18px 30px;
            border-radius: 50px;
            border: 1px solid rgba(255, 255, 255, 0.2);
            animation: modernPulse 2s ease-in-out infinite;
            z-index: 50;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            font-family: 'Poppins', sans-serif;
          "
        >
          💖 Pihu, click on the heart to see your surprise! 💖
        </div>

        <!-- Special Birthday Greeting -->
        <div
          id="birthday-greeting"
          style="
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: rgba(255, 255, 255, 0.15);
            backdrop-filter: blur(25px);
            color: rgba(255, 255, 255, 0.95);
            padding: 50px 60px;
            border-radius: 30px;
            text-align: center;
            font-size: 2.8rem;
            font-weight: 700;
            text-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
            box-shadow: 0 25px 50px rgba(0, 0, 0, 0.15);
            z-index: 1000;
            animation: fadeInScale 1s ease-out;
            border: 1px solid rgba(255, 255, 255, 0.2);
            font-family: 'Poppins', sans-serif;
            background-image: linear-gradient(
              45deg,
              rgba(255, 107, 107, 0.1),
              rgba(78, 205, 196, 0.1)
            );
          "
        >
          🎉 Happy 20th Birthday Pihu! 🎉<br />
          <span
            style="
              font-size: 1.3rem;
              margin-top: 20px;
              font-weight: 400;
              opacity: 0.9;
              letter-spacing: 1px;
              display: block;
            "
          >
            Welcome to your amazing 20s! 💖✨
          </span>
        </div>

        <canvas id="canvas" width="1100" height="680"></canvas>
      </div>
    </div>

    <script>
      (function () {
        var canvas = $("#canvas");

        if (!canvas[0].getContext) {
          $("#error").show();
          return false;
        }

        var width = canvas.width();
        var height = canvas.height();
        canvas.attr("width", width);
        canvas.attr("height", height);

        var opts = {
          seed: {
            x: width / 2 - 20,
            color: "rgb(255, 20, 147)", // Brighter pink color
            scale: 3, // Larger heart
          },
          branch: [
            [
              535,
              680,
              570,
              250,
              500,
              200,
              30,
              100,
              [
                [
                  540,
                  500,
                  455,
                  417,
                  340,
                  400,
                  13,
                  100,
                  [[450, 435, 434, 430, 394, 395, 2, 40]],
                ],
                [
                  550,
                  445,
                  600,
                  356,
                  680,
                  345,
                  12,
                  100,
                  [[578, 400, 648, 409, 661, 426, 3, 80]],
                ],
                [539, 281, 537, 248, 534, 217, 3, 40],
                [
                  546,
                  397,
                  413,
                  247,
                  328,
                  244,
                  9,
                  80,
                  [
                    [427, 286, 383, 253, 371, 205, 2, 40],
                    [498, 345, 435, 315, 395, 330, 4, 60],
                  ],
                ],
                [
                  546,
                  357,
                  608,
                  252,
                  678,
                  221,
                  6,
                  100,
                  [[590, 293, 646, 277, 648, 271, 2, 80]],
                ],
              ],
            ],
          ],
          bloom: {
            num: 700,
            width: 1080,
            height: 650,
          },
          footer: {
            width: 1200,
            height: 5,
            speed: 10,
          },
        };

        var tree = new Tree(canvas[0], width, height, opts);
        var seed = tree.seed;
        var foot = tree.footer;
        var hold = 1;

        canvas
          .click(function (e) {
            playAudio();
            var offset = canvas.offset(),
              x,
              y;
            x = e.pageX - offset.left;
            y = e.pageY - offset.top;
            if (seed.hover(x, y)) {
              hold = 0;
              canvas.unbind("click");
              canvas.unbind("mousemove");
              canvas.removeClass("hand");
              // Hide click instruction
              var instruction = document.getElementById("click-instruction");
              if (instruction) {
                instruction.style.display = "none";
              }
              // Start the tree and text animation only after click
              startTreeAnimation();
            }
          })
          .mousemove(function (e) {
            var offset = canvas.offset(),
              x,
              y;
            x = e.pageX - offset.left;
            y = e.pageY - offset.top;
            canvas.toggleClass("hand", seed.hover(x, y));
          });

        var seedAnimate = eval(
          Jscex.compile("async", function () {
            seed.draw();
            while (hold) {
              $await(Jscex.Async.sleep(10));
            }
            while (seed.canScale()) {
              seed.scale(0.95);
              $await(Jscex.Async.sleep(10));
            }
            while (seed.canMove()) {
              seed.move(0, 2);
              foot.draw();
              $await(Jscex.Async.sleep(10));
            }
          })
        );

        var growAnimate = eval(
          Jscex.compile("async", function () {
            do {
              tree.grow();
              $await(Jscex.Async.sleep(10));
            } while (tree.canGrow());
          })
        );

        var flowAnimate = eval(
          Jscex.compile("async", function () {
            do {
              tree.flower(2);
              $await(Jscex.Async.sleep(10));
            } while (tree.canFlower());
          })
        );

        var moveAnimate = eval(
          Jscex.compile("async", function () {
            tree.snapshot("p1", 240, 0, 610, 680);
            while (tree.move("p1", 500, 0)) {
              foot.draw();
              $await(Jscex.Async.sleep(10));
            }
            foot.draw();
            tree.snapshot("p2", 500, 0, 610, 680);

            canvas
              .parent()
              .css("background", "url(" + tree.toDataURL("image/png") + ")");
            canvas.css("background", "#ffe");
            $await(Jscex.Async.sleep(300));
            canvas.css("background", "none");
          })
        );

        var textAnimate = eval(
          Jscex.compile("async", function () {
            $("#code").show().typewriter();
          })
        );

        var runAsync = eval(
          Jscex.compile("async", function () {
            $await(seedAnimate());
            $await(growAnimate());
            $await(flowAnimate());
            $await(moveAnimate());
            textAnimate().start();
          })
        );

        // Draw the initial heart/seed so it's visible for clicking
        seed.draw();

        // Add pulsing effect to make heart more noticeable
        var heartPulse = setInterval(function () {
          if (hold === 1) {
            // Only pulse if animation hasn't started
            seed.draw();
            ctx.save();
            ctx.globalAlpha = 0.3;
            ctx.shadowColor = "#ff1493";
            ctx.shadowBlur = 20;
            seed.draw();
            ctx.restore();
          } else {
            clearInterval(heartPulse);
          }
        }, 1000);

        // Don't start automatically - wait for user click
        window.startTreeAnimation = function () {
          runAsync().start();
        };
      })();

      // Initialize Pihu's birthday countdown
      $(function () {
        // Pihu's birth date: August 15, 2005
        var birthDate = "08/15/2005";

        // Update the clock every second
        setInterval(function () {
          timeElapse(birthDate);
        }, 1000);

        // Initial call
        timeElapse(birthDate);

        // Hide birthday greeting after 4 seconds
        setTimeout(function () {
          var greeting = document.getElementById("birthday-greeting");
          if (greeting) {
            greeting.style.animation = "fadeOut 1s ease-in forwards";
            setTimeout(function () {
              greeting.style.display = "none";
            }, 1000);
          }
        }, 4000);
      });
    </script>
  </body>
</html>
