body {
  margin: 0;
  padding: 0;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  background-attachment: fixed;
  background-size: cover;
  background-repeat: no-repeat;
  font-size: 14px;
  font-family: "Poppins", sans-serif;
  color: white;
  overflow: auto;
  min-height: 100vh;
  position: relative;
}

body::before {
  content: "";
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image: url("../img2.png");
  background-size: cover;
  background-repeat: no-repeat;
  background-position: center;
  opacity: 0.3;
  z-index: -1;
}
a {
  color: white;
  font-size: 14px;
}
#main {
  width: 100%;
}
#wrap {
  position: relative;
  margin: 0 auto;
  width: 1100px;
  height: 680px;
  margin-top: 10px;
}
#text {
  width: 350px;
  height: 560px;
  left: 150px;
  top: 80px;
  position: absolute;
  z-index: 10;
}
#code {
  display: none;
  color: #fff;
  font-size: 18px;
  margin-top: 70px;
  line-height: 35px;
  font-weight: 600;
  font-family: "Dancing Script", cursive;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.7), 0 0 15px rgba(255, 255, 255, 0.3);
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  padding: 30px;
  border-radius: 20px;
  border: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
}
#clock-box {
  position: absolute;
  left: 60px;
  top: 550px;
  font-size: 24px;
  display: none;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  padding: 15px 25px;
  border-radius: 15px;
  border: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
  font-family: "Poppins", sans-serif;
  font-weight: 600;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
}
#clock-box a {
  font-size: 24px;
  text-decoration: none;
  color: #ff69b4;
}
#clock {
  margin-left: 20px;
  color: #fff;
}
#clock .digit {
  font-size: 32px;
  color: #ff69b4;
  text-shadow: 0 0 10px rgba(255, 105, 180, 0.5);
}
#canvas {
  margin: 0 auto;
  width: 1100px;
  height: 680px;
  border-radius: 20px;
  box-shadow: 0 10px 40px rgba(0, 0, 0, 0.3);
}
#error {
  margin: 0 auto;
  text-align: center;
  margin-top: 60px;
  display: none;
}
.hand {
  cursor: pointer;
}
.say {
  margin-left: 5px;
  display: inline-block;
  animation: fadeInUp 0.8s ease-out forwards;
  opacity: 0;
  transform: translateY(20px);
}

.say:nth-child(1) {
  animation-delay: 0.2s;
}
.say:nth-child(2) {
  animation-delay: 0.4s;
}
.say:nth-child(3) {
  animation-delay: 0.6s;
}
.say:nth-child(4) {
  animation-delay: 0.8s;
}
.say:nth-child(5) {
  animation-delay: 1s;
}
.say:nth-child(6) {
  animation-delay: 1.2s;
}
.say:nth-child(7) {
  animation-delay: 1.4s;
}
.say:nth-child(8) {
  animation-delay: 1.6s;
}

@keyframes fadeInUp {
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.space {
  margin-right: 150px;
}

#message-box {
  position: absolute;
  margin-top: 450px;
  font-size: 25px;
  font-family: "Dancing Script", cursive;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  padding: 20px;
  border-radius: 15px;
  border: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
}

/* Responsive Design */
@media (max-width: 1200px) {
  #wrap {
    width: 90%;
    max-width: 1100px;
  }

  #canvas {
    width: 100%;
    max-width: 1100px;
  }

  #text {
    width: 300px;
    left: 50px;
  }

  .main-title {
    font-size: 2.5rem;
  }
}

@media (max-width: 768px) {
  #wrap {
    width: 95%;
    height: auto;
    min-height: 500px;
  }

  #canvas {
    width: 100%;
    height: 500px;
  }

  #text {
    width: 280px;
    left: 20px;
    top: 50px;
  }

  #code {
    font-size: 16px;
    line-height: 30px;
    padding: 20px;
  }

  .main-title {
    font-size: 2rem;
  }

  .click-instruction {
    bottom: 20px;
    padding: 10px 20px;
    font-size: 14px;
  }

  #clock-box {
    left: 20px;
    top: 450px;
    font-size: 18px;
  }
}

@media (max-width: 480px) {
  .main-title {
    font-size: 1.5rem;
  }

  #text {
    width: 250px;
    left: 10px;
  }

  #code {
    font-size: 14px;
    line-height: 25px;
    padding: 15px;
  }

  .music-controls {
    top: 10px;
    right: 10px;
  }

  .title-container {
    top: 10px;
  }
}
