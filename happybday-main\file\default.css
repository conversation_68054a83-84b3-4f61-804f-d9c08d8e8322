body {
  margin: 0;
  padding: 0;
  background-image: url("../img2.png");
  background-size: cover;
  background-repeat: no-repeat;
  font-size: 14px;
  font-family: sans-serif;
  color: white;
  overflow: auto;
}
a {
  color: white;
  font-size: 14px;
}
#main {
  width: 100%;
}
#wrap {
  position: relative;
  margin: 0 auto;
  width: 1100px;
  height: 680px;
  margin-top: 10px;
}
#text {
  width: 272px;
  height: 560px;
  left: 188px;
  top: 80px;
  position: absolute;
}
#code {
  display: none;
  color: rgb(196, 255, 255);
  font-size: 16px;
  margin-top: 70px;
  line-height: 25px;
  font-size: larger;
  font-weight: bolder;
}
#clock-box {
  position: absolute;
  left: 60px;
  top: 550px;
  font-size: 28px;
  display: none;
}
#clock-box a {
  font-size: 28px;
  text-decoration: none;
}
#clock {
  margin-left: 48px;
}
#clock .digit {
  font-size: 64px;
}
#canvas {
  margin: 0 auto;
  width: 1100px;
  height: 680px;
}
#error {
  margin: 0 auto;
  text-align: center;
  margin-top: 60px;
  display: none;
}
.hand {
  cursor: pointer;
}
.say {
  margin-left: 5px;
}
.space {
  margin-right: 150px;
}

#message-box {
  position: absolute;
  margin-top: 450px;
  font-size: 25px;
  font-family: monospace;
}
