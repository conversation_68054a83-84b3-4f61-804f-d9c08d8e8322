body {
  margin: 0;
  padding: 0;
  background-image: url("../img2.png");
  background-size: cover;
  background-repeat: no-repeat;
  font-size: 14px;
  font-family: sans-serif;
  color: white;
  overflow: auto;
}
a {
  color: white;
  font-size: 14px;
}
#main {
  width: 100%;
}
#wrap {
  position: relative;
  margin: 0 auto;
  width: 1100px;
  height: 680px;
  margin-top: 10px;
}
#text {
  width: 350px;
  height: 560px;
  left: 150px;
  top: 80px;
  position: absolute;
  background: rgba(255, 255, 255, 0.15);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 25px;
  padding: 30px 35px;
  box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
  z-index: 1000;
}
#code {
  display: none;
  color: rgba(255, 255, 255, 0.95);
  font-family: "Poppins", sans-serif;
  font-size: 18px;
  margin-top: 40px;
  line-height: 32px;
  font-weight: 500;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
  text-align: center;
  z-index: 1001;
  position: relative;
}
#clock-box {
  position: absolute;
  left: 60px;
  top: 550px;
  font-size: 28px;
  display: none;
}
#clock-box a {
  font-size: 28px;
  text-decoration: none;
}
#clock {
  margin-left: 48px;
}
#clock .digit {
  font-size: 64px;
}
#canvas {
  margin: 0 auto;
  width: 1100px;
  height: 680px;
}
#error {
  margin: 0 auto;
  text-align: center;
  margin-top: 60px;
  display: none;
}
.hand {
  cursor: pointer;
}
.say {
  margin-left: 5px;
  color: rgba(255, 255, 255, 0.95);
  font-weight: 500;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
  line-height: 1.6;
  display: inline-block;
  margin-bottom: 8px;
}
.space {
  margin-right: 150px;
}

#message-box {
  position: absolute;
  margin-top: 450px;
  font-size: 25px;
  font-family: monospace;
}
