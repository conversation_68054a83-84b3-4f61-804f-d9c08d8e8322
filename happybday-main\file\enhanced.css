/* Enhanced Birthday Page Styles */

/* Additional particle effects */
.particle {
  position: absolute;
  width: 6px;
  height: 6px;
  background: radial-gradient(circle, #ff69b4, #ff1493);
  border-radius: 50%;
  animation: particleFloat 4s ease-in-out infinite;
  opacity: 0.8;
}

@keyframes particleFloat {
  0%, 100% {
    transform: translateY(0) rotate(0deg);
    opacity: 0;
  }
  10% {
    opacity: 0.8;
  }
  50% {
    transform: translateY(-50px) rotate(180deg);
    opacity: 1;
  }
  90% {
    opacity: 0.8;
  }
}

/* Enhanced hover effects */
#canvas:hover {
  transform: scale(1.02);
  transition: transform 0.3s ease;
}

/* Love message animations */
.love-message {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  font-family: 'Great Vibes', cursive;
  font-size: 4rem;
  color: #ff69b4;
  text-shadow: 0 0 30px rgba(255,105,180,0.8);
  opacity: 0;
  z-index: 1001;
  pointer-events: none;
  animation: loveMessagePulse 3s ease-in-out;
}

@keyframes loveMessagePulse {
  0%, 100% {
    opacity: 0;
    transform: translate(-50%, -50%) scale(0.5);
  }
  50% {
    opacity: 1;
    transform: translate(-50%, -50%) scale(1);
  }
}

/* Birthday cake animation */
.birthday-cake {
  position: fixed;
  bottom: 100px;
  right: 50px;
  font-size: 3rem;
  animation: cakeBounce 2s ease-in-out infinite;
  z-index: 100;
}

@keyframes cakeBounce {
  0%, 100% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-20px);
  }
}

/* Balloon animations */
.balloon {
  position: fixed;
  font-size: 2rem;
  animation: balloonFloat 6s ease-in-out infinite;
  z-index: 50;
}

.balloon:nth-child(1) {
  left: 10%;
  animation-delay: 0s;
}

.balloon:nth-child(2) {
  left: 20%;
  animation-delay: 1s;
}

.balloon:nth-child(3) {
  left: 80%;
  animation-delay: 2s;
}

.balloon:nth-child(4) {
  left: 90%;
  animation-delay: 3s;
}

@keyframes balloonFloat {
  0%, 100% {
    transform: translateY(100vh);
  }
  50% {
    transform: translateY(-100px);
  }
}

/* Enhanced text effects */
.typewriter-effect {
  overflow: hidden;
  border-right: 3px solid #ff69b4;
  white-space: nowrap;
  margin: 0 auto;
  animation: typing 3.5s steps(40, end), blink-caret 0.75s step-end infinite;
}

@keyframes typing {
  from { width: 0; }
  to { width: 100%; }
}

@keyframes blink-caret {
  from, to { border-color: transparent; }
  50% { border-color: #ff69b4; }
}

/* Romantic background patterns */
body::after {
  content: '';
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image: 
    radial-gradient(circle at 20% 80%, rgba(255,105,180,0.1) 0%, transparent 50%),
    radial-gradient(circle at 80% 20%, rgba(255,20,147,0.1) 0%, transparent 50%),
    radial-gradient(circle at 40% 40%, rgba(255,182,193,0.1) 0%, transparent 50%);
  z-index: -2;
  pointer-events: none;
}

/* Interactive elements */
.interactive-heart {
  position: fixed;
  font-size: 2rem;
  cursor: pointer;
  transition: all 0.3s ease;
  z-index: 200;
}

.interactive-heart:hover {
  transform: scale(1.5);
  filter: drop-shadow(0 0 20px rgba(255,105,180,0.8));
}

/* Loading animation */
.loading-heart {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  font-size: 5rem;
  color: #ff69b4;
  animation: heartbeat 1s ease-in-out infinite;
  z-index: 2000;
}

@keyframes heartbeat {
  0%, 100% {
    transform: translate(-50%, -50%) scale(1);
  }
  50% {
    transform: translate(-50%, -50%) scale(1.2);
  }
}

/* Confetti effect */
.confetti {
  position: fixed;
  width: 10px;
  height: 10px;
  background: #ff69b4;
  animation: confettiFall 3s linear infinite;
  z-index: 1000;
}

.confetti:nth-child(odd) {
  background: #ff1493;
  animation-delay: 0.5s;
}

.confetti:nth-child(even) {
  background: #ffc0cb;
  animation-delay: 1s;
}

@keyframes confettiFall {
  0% {
    transform: translateY(-100vh) rotate(0deg);
    opacity: 1;
  }
  100% {
    transform: translateY(100vh) rotate(720deg);
    opacity: 0;
  }
}

/* Mobile optimizations */
@media (max-width: 768px) {
  .love-message {
    font-size: 2.5rem;
  }
  
  .birthday-cake {
    bottom: 50px;
    right: 20px;
    font-size: 2rem;
  }
  
  .balloon {
    font-size: 1.5rem;
  }
  
  .interactive-heart {
    font-size: 1.5rem;
  }
  
  .loading-heart {
    font-size: 3rem;
  }
}

/* Accessibility improvements */
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

/* Print styles */
@media print {
  .floating-hearts,
  .music-controls,
  .click-instruction,
  .birthday-cake,
  .balloon {
    display: none;
  }
  
  body {
    background: white;
    color: black;
  }
  
  .main-title {
    color: #333;
  }
}
