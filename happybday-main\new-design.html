<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Happy 20th Birthday Pihu! 🎉💖</title>
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;600;700;800&display=swap" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: 'Poppins', sans-serif;
            overflow-x: hidden;
            position: relative;
        }
        
        /* Animated background particles */
        .particles {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            z-index: 1;
        }
        
        .particle {
            position: absolute;
            width: 4px;
            height: 4px;
            background: rgba(255, 255, 255, 0.6);
            border-radius: 50%;
            animation: float 6s ease-in-out infinite;
        }
        
        .particle:nth-child(1) { left: 10%; animation-delay: 0s; }
        .particle:nth-child(2) { left: 20%; animation-delay: 1s; }
        .particle:nth-child(3) { left: 30%; animation-delay: 2s; }
        .particle:nth-child(4) { left: 40%; animation-delay: 3s; }
        .particle:nth-child(5) { left: 50%; animation-delay: 4s; }
        .particle:nth-child(6) { left: 60%; animation-delay: 5s; }
        .particle:nth-child(7) { left: 70%; animation-delay: 0.5s; }
        .particle:nth-child(8) { left: 80%; animation-delay: 1.5s; }
        .particle:nth-child(9) { left: 90%; animation-delay: 2.5s; }
        
        @keyframes float {
            0%, 100% { transform: translateY(100vh) rotate(0deg); opacity: 0; }
            10% { opacity: 1; }
            90% { opacity: 1; }
            100% { transform: translateY(-100px) rotate(360deg); opacity: 0; }
        }
        
        .container {
            position: relative;
            z-index: 2;
            min-height: 100vh;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }
        
        /* Header Section */
        .header {
            text-align: center;
            margin-bottom: 40px;
        }
        
        .main-title {
            font-size: 4rem;
            font-weight: 800;
            background: linear-gradient(45deg, #ff6b6b, #ff8e8e, #ffa8a8);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            margin-bottom: 10px;
            animation: titleGlow 3s ease-in-out infinite alternate;
            text-shadow: 0 4px 8px rgba(0,0,0,0.3);
        }
        
        .subtitle {
            font-size: 1.3rem;
            color: rgba(255, 255, 255, 0.9);
            font-weight: 300;
            letter-spacing: 3px;
            text-transform: uppercase;
        }
        
        @keyframes titleGlow {
            0% { filter: drop-shadow(0 0 10px rgba(255, 107, 107, 0.5)); }
            100% { filter: drop-shadow(0 0 20px rgba(255, 107, 107, 0.8)); }
        }
        
        /* Birthday Card */
        .birthday-card {
            background: rgba(255, 255, 255, 0.15);
            backdrop-filter: blur(25px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 30px;
            padding: 50px;
            max-width: 700px;
            width: 100%;
            text-align: center;
            box-shadow: 0 25px 50px rgba(0, 0, 0, 0.15);
            animation: cardFloat 6s ease-in-out infinite;
            position: relative;
            overflow: hidden;
        }
        
        .birthday-card::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: linear-gradient(45deg, transparent, rgba(255, 255, 255, 0.1), transparent);
            animation: shimmer 4s linear infinite;
            pointer-events: none;
        }
        
        @keyframes cardFloat {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-15px); }
        }
        
        @keyframes shimmer {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        /* Age Display */
        .age-display {
            font-size: 5rem;
            font-weight: 900;
            background: linear-gradient(45deg, #ff6b6b, #4ecdc4, #45b7d1, #f093fb);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            margin: 30px 0;
            animation: ageGlow 2s ease-in-out infinite alternate;
            position: relative;
        }
        
        .age-display::after {
            content: '🎂';
            position: absolute;
            right: -60px;
            top: 50%;
            transform: translateY(-50%);
            font-size: 3rem;
            animation: bounce 2s ease-in-out infinite;
        }
        
        @keyframes ageGlow {
            0% { filter: drop-shadow(0 0 15px rgba(255, 107, 107, 0.6)); }
            100% { filter: drop-shadow(0 0 25px rgba(78, 205, 196, 0.8)); }
        }
        
        @keyframes bounce {
            0%, 100% { transform: translateY(-50%) scale(1); }
            50% { transform: translateY(-60%) scale(1.1); }
        }
        
        /* Message Text */
        .birthday-message {
            color: rgba(255, 255, 255, 0.95);
            font-size: 1.2rem;
            line-height: 2;
            margin: 40px 0;
            font-weight: 400;
            text-shadow: 0 2px 4px rgba(0,0,0,0.3);
        }
        
        .highlight {
            background: linear-gradient(45deg, #ff6b6b, #4ecdc4);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            font-weight: 600;
        }
        
        /* Interactive Button */
        .magic-button {
            background: linear-gradient(45deg, #ff6b6b, #4ecdc4);
            border: none;
            padding: 18px 50px;
            border-radius: 50px;
            color: white;
            font-size: 1.2rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 15px 35px rgba(255, 107, 107, 0.4);
            animation: buttonPulse 2s ease-in-out infinite;
            margin: 30px 0;
            text-transform: uppercase;
            letter-spacing: 1px;
        }
        
        .magic-button:hover {
            transform: translateY(-5px);
            box-shadow: 0 20px 45px rgba(255, 107, 107, 0.6);
        }
        
        @keyframes buttonPulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.05); }
        }
        
        /* Clock Display */
        .clock-display {
            position: fixed;
            top: 30px;
            right: 30px;
            background: rgba(255, 255, 255, 0.15);
            backdrop-filter: blur(20px);
            padding: 15px 25px;
            border-radius: 20px;
            border: 1px solid rgba(255, 255, 255, 0.2);
            color: white;
            font-weight: 600;
            z-index: 100;
            font-size: 0.9rem;
        }
        
        /* Message Box */
        .blessing-message {
            position: fixed;
            bottom: 30px;
            left: 30px;
            background: rgba(255, 255, 255, 0.15);
            backdrop-filter: blur(20px);
            padding: 15px 20px;
            border-radius: 15px;
            border: 1px solid rgba(255, 255, 255, 0.2);
            color: rgba(255, 255, 255, 0.9);
            font-size: 0.95rem;
            font-weight: 500;
            z-index: 100;
            max-width: 280px;
            animation: messageGlow 3s ease-in-out infinite alternate;
        }
        
        @keyframes messageGlow {
            0% { box-shadow: 0 0 15px rgba(255, 255, 255, 0.2); }
            100% { box-shadow: 0 0 25px rgba(255, 255, 255, 0.4); }
        }
        
        /* Responsive Design */
        @media (max-width: 768px) {
            .main-title { font-size: 2.8rem; }
            .age-display { font-size: 3.5rem; }
            .birthday-card { padding: 30px 25px; margin: 20px; }
            .birthday-message { font-size: 1rem; }
            .clock-display, .blessing-message { position: relative; margin: 20px auto; }
        }
    </style>
</head>
<body>
    <!-- Animated Particles -->
    <div class="particles">
        <div class="particle"></div>
        <div class="particle"></div>
        <div class="particle"></div>
        <div class="particle"></div>
        <div class="particle"></div>
        <div class="particle"></div>
        <div class="particle"></div>
        <div class="particle"></div>
        <div class="particle"></div>
    </div>

    <!-- Main Container -->
    <div class="container">
        <!-- Header -->
        <div class="header">
            <h1 class="main-title">Happy Birthday Pihu!</h1>
            <p class="subtitle">Celebrating You ✨</p>
        </div>

        <!-- Birthday Card -->
        <div class="birthday-card">
            <div class="age-display">20</div>
            
            <div class="birthday-message">
                Dear <span class="highlight">Yogita</span> (my dearest Pihu),<br><br>
                
                🌟 Today marks your <span class="highlight">20th birthday</span> - what an incredible milestone! 🌟<br><br>
                
                On this special day, <span class="highlight">August 15th</span>, I want you to know how amazing you are.<br>
                You're not just my bestie, you're the most wonderful person I know! 💖<br><br>
                
                <span class="highlight">Welcome to your 20s!</span> This decade is going to be absolutely magical for you.<br>
                May it be filled with endless joy, success, love, and all your dreams coming true! ✨<br><br>
                
                Thank you for being such an incredible friend. Here's to many more years of friendship! 🥳
            </div>
            
            <button class="magic-button" onclick="showSurprise()">
                🎁 Click for Your Surprise! 🎁
            </button>
        </div>
    </div>

    <!-- Clock Display -->
    <div class="clock-display" id="clock">
        <span id="clock-text">Loading...</span>
    </div>

    <!-- Blessing Message -->
    <div class="blessing-message">
        💫 Pihu has been blessing the world since August 15, 2005 💫
    </div>

    <script>
        // Birthday countdown
        function updateClock() {
            const birthDate = new Date('2005-08-15');
            const now = new Date();
            const diff = now - birthDate;
            
            const days = Math.floor(diff / (1000 * 60 * 60 * 24));
            const hours = Math.floor((diff % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
            const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60));
            const seconds = Math.floor((diff % (1000 * 60)) / 1000);
            
            document.getElementById('clock-text').innerHTML = 
                `${days} days ${hours}h ${minutes}m ${seconds}s`;
        }
        
        // Update clock every second
        setInterval(updateClock, 1000);
        updateClock();
        
        // Surprise function
        function showSurprise() {
            alert('🎉 Happy 20th Birthday Pihu! 🎉\n\nYou are absolutely amazing and deserve all the happiness in the world!\n\nWith love from your bestie! 💖');
            
            // Create confetti effect
            createConfetti();
        }
        
        // Confetti effect
        function createConfetti() {
            for (let i = 0; i < 50; i++) {
                setTimeout(() => {
                    const confetti = document.createElement('div');
                    confetti.style.position = 'fixed';
                    confetti.style.left = Math.random() * 100 + 'vw';
                    confetti.style.top = '-10px';
                    confetti.style.width = '10px';
                    confetti.style.height = '10px';
                    confetti.style.backgroundColor = ['#ff6b6b', '#4ecdc4', '#45b7d1', '#f093fb'][Math.floor(Math.random() * 4)];
                    confetti.style.borderRadius = '50%';
                    confetti.style.zIndex = '1000';
                    confetti.style.animation = 'confettiFall 3s linear forwards';
                    
                    document.body.appendChild(confetti);
                    
                    setTimeout(() => {
                        confetti.remove();
                    }, 3000);
                }, i * 100);
            }
        }
        
        // Add confetti animation
        const style = document.createElement('style');
        style.textContent = `
            @keyframes confettiFall {
                to {
                    transform: translateY(100vh) rotate(360deg);
                    opacity: 0;
                }
            }
        `;
        document.head.appendChild(style);
    </script>
</body>
</html>
