// Additional Interactive Features for Birthday Page

// Keyboard shortcuts
document.addEventListener('keydown', function(e) {
    switch(e.key.toLowerCase()) {
        case ' ': // Spacebar
            e.preventDefault();
            if (typeof showLoveMessage === 'function') {
                showLoveMessage();
            }
            break;
        case 'm': // M key for music toggle
            e.preventDefault();
            if (typeof toggleAudio === 'function') {
                toggleAudio();
            }
            break;
        case 'c': // C key for confetti
            e.preventDefault();
            if (typeof createConfetti === 'function') {
                createConfetti();
            }
            break;
        case 'h': // H key for hearts
            e.preventDefault();
            createHeartBurst();
            break;
    }
});

// Create heart burst effect
function createHeartBurst() {
    const centerX = window.innerWidth / 2;
    const centerY = window.innerHeight / 2;
    const hearts = ['💖', '💕', '💗', '💓', '💝', '💘', '💞'];
    
    for (let i = 0; i < 12; i++) {
        const heart = document.createElement('div');
        heart.innerHTML = hearts[Math.floor(Math.random() * hearts.length)];
        heart.style.position = 'fixed';
        heart.style.left = centerX + 'px';
        heart.style.top = centerY + 'px';
        heart.style.fontSize = '24px';
        heart.style.pointerEvents = 'none';
        heart.style.zIndex = '2000';
        heart.style.color = '#ff69b4';
        
        const angle = (i / 12) * 2 * Math.PI;
        const distance = 100 + Math.random() * 100;
        const endX = centerX + Math.cos(angle) * distance;
        const endY = centerY + Math.sin(angle) * distance;
        
        heart.animate([
            { 
                transform: 'translate(-50%, -50%) scale(0) rotate(0deg)',
                opacity: 1 
            },
            { 
                transform: `translate(${endX - centerX}px, ${endY - centerY}px) scale(1.5) rotate(360deg)`,
                opacity: 0 
            }
        ], {
            duration: 2000,
            easing: 'ease-out'
        }).onfinish = () => heart.remove();
        
        document.body.appendChild(heart);
    }
}

// Double click effects
document.addEventListener('dblclick', function(e) {
    // Create sparkle effect at click position
    createSparkleAt(e.clientX, e.clientY);
});

function createSparkleAt(x, y) {
    for (let i = 0; i < 8; i++) {
        const sparkle = document.createElement('div');
        sparkle.style.position = 'fixed';
        sparkle.style.left = x + 'px';
        sparkle.style.top = y + 'px';
        sparkle.style.width = '4px';
        sparkle.style.height = '4px';
        sparkle.style.background = '#fff';
        sparkle.style.borderRadius = '50%';
        sparkle.style.pointerEvents = 'none';
        sparkle.style.zIndex = '1500';
        
        const angle = (i / 8) * 2 * Math.PI;
        const distance = 30 + Math.random() * 20;
        const endX = x + Math.cos(angle) * distance;
        const endY = y + Math.sin(angle) * distance;
        
        sparkle.animate([
            { 
                transform: 'translate(-50%, -50%) scale(0)',
                opacity: 1 
            },
            { 
                transform: `translate(${endX - x}px, ${endY - y}px) scale(1)`,
                opacity: 0 
            }
        ], {
            duration: 1000,
            easing: 'ease-out'
        }).onfinish = () => sparkle.remove();
        
        document.body.appendChild(sparkle);
    }
}

// Mouse trail effect
let mouseTrail = [];
const maxTrailLength = 10;

document.addEventListener('mousemove', function(e) {
    // Throttle mouse trail for performance
    if (Math.random() > 0.7) {
        mouseTrail.push({ x: e.clientX, y: e.clientY, time: Date.now() });
        
        if (mouseTrail.length > maxTrailLength) {
            mouseTrail.shift();
        }
        
        // Create small heart trail
        if (Math.random() > 0.95) {
            const trailHeart = document.createElement('div');
            trailHeart.innerHTML = '💖';
            trailHeart.style.position = 'fixed';
            trailHeart.style.left = e.clientX + 'px';
            trailHeart.style.top = e.clientY + 'px';
            trailHeart.style.fontSize = '12px';
            trailHeart.style.pointerEvents = 'none';
            trailHeart.style.zIndex = '500';
            trailHeart.style.opacity = '0.6';
            trailHeart.style.transform = 'translate(-50%, -50%)';
            
            trailHeart.animate([
                { opacity: 0.6, transform: 'translate(-50%, -50%) scale(1)' },
                { opacity: 0, transform: 'translate(-50%, -50%) scale(0.5)' }
            ], {
                duration: 1500,
                easing: 'ease-out'
            }).onfinish = () => trailHeart.remove();
            
            document.body.appendChild(trailHeart);
        }
    }
});

// Touch support for mobile
document.addEventListener('touchstart', function(e) {
    if (e.touches.length === 2) {
        // Two finger touch - create heart burst
        e.preventDefault();
        createHeartBurst();
    }
});

// Add ripple effect to interactive elements
function addRippleEffect(element) {
    element.addEventListener('click', function(e) {
        const ripple = document.createElement('div');
        const rect = element.getBoundingClientRect();
        const size = Math.max(rect.width, rect.height);
        const x = e.clientX - rect.left - size / 2;
        const y = e.clientY - rect.top - size / 2;
        
        ripple.style.width = ripple.style.height = size + 'px';
        ripple.style.left = x + 'px';
        ripple.style.top = y + 'px';
        ripple.style.position = 'absolute';
        ripple.style.borderRadius = '50%';
        ripple.style.background = 'rgba(255, 105, 180, 0.3)';
        ripple.style.transform = 'scale(0)';
        ripple.style.pointerEvents = 'none';
        ripple.style.zIndex = '1';
        
        element.style.position = 'relative';
        element.style.overflow = 'hidden';
        element.appendChild(ripple);
        
        ripple.animate([
            { transform: 'scale(0)', opacity: 1 },
            { transform: 'scale(1)', opacity: 0 }
        ], {
            duration: 600,
            easing: 'ease-out'
        }).onfinish = () => ripple.remove();
    });
}

// Initialize ripple effects when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    // Add ripple to interactive hearts
    document.querySelectorAll('.interactive-heart').forEach(addRippleEffect);
    
    // Add ripple to music button
    const musicBtn = document.querySelector('.music-btn');
    if (musicBtn) {
        addRippleEffect(musicBtn);
    }
});

// Performance monitoring
let performanceWarningShown = false;

function checkPerformance() {
    if (performance.now() > 10000 && !performanceWarningShown) {
        const elementCount = document.querySelectorAll('.heart, .sparkle, .particle, .confetti').length;
        if (elementCount > 50) {
            console.log('Performance warning: Many animated elements detected');
            performanceWarningShown = true;
        }
    }
}

setInterval(checkPerformance, 5000);

// Export functions for global access
window.createHeartBurst = createHeartBurst;
window.createSparkleAt = createSparkleAt;
